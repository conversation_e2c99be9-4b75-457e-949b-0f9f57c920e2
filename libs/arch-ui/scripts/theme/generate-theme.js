const fs = require('fs');
const path = require('path');

const colorVariablesPath = path.resolve(__dirname, '../../src/theme/colors-variables.css');
const themeVariablesPath = path.resolve(__dirname, '../../src/theme/theme-variables.ts');

// Read the JSON file
const data = JSON.parse(fs.readFileSync(path.join(__dirname, './color-tokens.json'), 'utf8'));

const getVariableName = (name) => {
  return `--${name.replace(/\//g, '-')}`;
};

// Function to generate CSS variables
const generateCSSVariables = (colors) => {
  let cssVariables = '';
  colors.forEach((color) => {
    const colorVar = `var(--color-${color.var})`;
    cssVariables += `${getVariableName(color.name)}: ${colorVar};\n`;
  });
  return cssVariables;
};

// Function to generate CSS for light mode
const generateLightModeCSS = (colors) => {
  return `
    :root {
      ${generateCSSVariables(colors)}
    }
  `;
};

// Function to generate CSS for dark mode
const generateDarkModeCSS = (colors) => {
  return `
  [data-theme='dark'] {
    ${generateCSSVariables(colors)}
  }
  `;
};

// Function to generate Tailwind CSS
const generateTailwindCSS = ([
  {
    values: [{ color: lightTheme }, { color: darkTheme }],
  },
]) => {
  return `
  @import "tailwindcss";
  
  @layer base {
      ${generateLightModeCSS(lightTheme)}
      
      ${generateDarkModeCSS(darkTheme)}
}
  `;
};

// Write the config to the output file
fs.writeFileSync(colorVariablesPath, generateTailwindCSS(data));

const themeKeys = {
  background: 'backgroundColor',
  border: 'borderColor',
  text: 'textColor',
  icon: 'textColor-icon',
  surface: 'backgroundColor-surface',
  skeleton: 'backgroundColor-skeleton',
  overlay: 'backgroundColor-overlay',
  link: 'textColor-link',
  interaction: 'textColor-interaction',
  stroke: 'strokeColor'
};

const getThemeKey = (key) => {
  const [, scope, ...segments] = key.split('/');

  if (segments.includes('default')) segments.pop();
  return [themeKeys[scope], ...segments].join('-');
};

const generateTheme = ([
  {
    values: [{ color: theme }],
  },
]) => {
  const result = {
    borderColor: {},
    textColor: {},
    backgroundColor: {},
    strokeColor: {},
  };

  theme.forEach((value) => {
    const themeValue = `var(${getVariableName(value.name)})`;
    const [scope, ...keys] = getThemeKey(value.name).split('-');
    const variableName = keys.join('-');
    result[scope][variableName] = themeValue;
  });

  return `
    const variables = ${JSON.stringify(result, null, 4)}

    export type Variables = typeof variables
    export { variables }
  `;
};

fs.writeFileSync(themeVariablesPath, generateTheme(data));
